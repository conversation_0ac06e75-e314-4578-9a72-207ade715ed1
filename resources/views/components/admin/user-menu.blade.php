@props(['mobile' => false])

@auth
    <flux:dropdown position="{{ $mobile ? 'top' : 'bottom' }}" align="{{ $mobile ? 'end' : 'start' }}">
        <flux:profile
            :name="$mobile ? null : auth()->user()->name"
            :initials="auth()->user()->initials()"
            icon-trailing="{{ $mobile ? 'chevron-down' : 'chevrons-up-down' }}"
        />

        <flux:menu class="{{ $mobile ? '' : 'w-[220px]' }}">
            {{-- Información del usuario --}}
            <flux:menu.radio.group>
                <div class="p-0 text-sm font-normal">
                    <div class="flex items-center gap-2 px-1 py-1.5 text-left text-sm">
                        <span class="relative flex h-8 w-8 shrink-0 overflow-hidden rounded-lg">
                            <span
                                class="flex h-full w-full items-center justify-center rounded-lg bg-neutral-200 text-black dark:bg-neutral-700 dark:text-white"
                            >
                                {{ auth()->user()->initials() }}
                            </span>
                        </span>

                        <div class="grid flex-1 text-left text-sm leading-tight">
                            <span class="truncate font-semibold">{{ auth()->user()->name }}</span>
                            <span class="truncate text-xs">{{ auth()->user()->email }}</span>
                        </div>
                    </div>
                </div>
            </flux:menu.radio.group>

            <flux:menu.separator/>

            {{-- Opciones del menú --}}
            <flux:menu.radio.group>
                <flux:menu.item href="/settings/profile" icon="cog">
                    {{ __('global.settings') }}
                </flux:menu.item>
            </flux:menu.radio.group>

            <flux:menu.separator/>

            {{-- Cerrar sesión --}}
            <form method="POST" action="{{ route('logout') }}" class="w-full">
                @csrf
                <flux:menu.item as="button" type="submit" icon="arrow-right-start-on-rectangle" class="w-full">
                    {{ __('global.log_out') }}
                </flux:menu.item>
            </form>
        </flux:menu>
    </flux:dropdown>
@endauth
