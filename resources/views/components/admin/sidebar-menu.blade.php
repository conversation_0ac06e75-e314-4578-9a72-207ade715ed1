@php
    use App\Helpers\MenuHelper;

    $menuConfig = MenuHelper::getMenuItems();
    $favoritesConfig = MenuHelper::getFavoritesConfig();
    $externalLinksConfig = MenuHelper::getExternalLinksConfig();
@endphp

<flux:navlist variant="outline">
    {{-- Renderizar elementos del menú principal --}}
    @foreach($menuConfig as $group)
        <flux:navlist.group
            heading="{{ isset($group['heading_key']) ? __($group['heading_key']) : $group['heading'] }}"
            class="{{ $group['class'] ?? 'grid' }}"
        >
            @foreach($group['items'] as $item)
                <flux:navlist.item
                    icon="{{ $item['icon'] }}"
                    :href="route('{{ $item['route'] }}')"
                    :current="request()->routeIs('{{ $item['current_check'] }}')"
                >
                    {{ isset($item['label_key']) ? __($item['label_key']) : $item['label'] }}
                </flux:navlist.item>
            @endforeach
        </flux:navlist.group>
    @endforeach

    {{-- Sección de Favoritos (si está habilitada) --}}
    @if($favoritesConfig['enabled'] ?? false)
        <flux:navlist.group 
            @if($favoritesConfig['expandable'] ?? false) expandable @endif
            heading="{{ $favoritesConfig['heading'] }}" 
            class="{{ $favoritesConfig['class'] ?? 'lg:grid' }}"
        >
            @foreach($favoritesConfig['items'] as $favorite)
                <flux:navlist.item href="{{ $favorite['url'] }}">
                    {{ $favorite['label'] }}
                </flux:navlist.item>
            @endforeach
        </flux:navlist.group>
    @endif
</flux:navlist>

{{-- Spacer --}}
<flux:spacer/>

{{-- Sección de impersonación --}}
@if (Session::has('admin_user_id'))
    <div class="py-2 flex items-center justify-center bg-zinc-100 dark:bg-zinc-600 dark:text-white mb-6 rounded">
        <form id="stop-impersonating" class="flex flex-col items-center gap-3" action="{{ route('impersonate.destroy') }}"
              method="POST">
            @csrf
            @method('DELETE')
            <p class="text-xs">
                {{ __('users.you_are_impersonating') }}:
                <strong>{{ auth()->user()->name }}</strong>
            </p>
            <flux:button type="submit" size="sm" variant="danger" form="stop-impersonating" class="!w-full !flex !flex-row">
                <div>
                    {{ __('users.stop_impersonating') }}
                </div>
            </flux:button>
        </form>
    </div>
@endif

{{-- Enlaces externos (si están habilitados) --}}
@if($externalLinksConfig['enabled'] ?? false)
    <flux:navlist variant="outline">
        @foreach($externalLinksConfig['items'] as $link)
            <flux:navlist.item 
                icon="{{ $link['icon'] }}" 
                href="{{ $link['url'] }}" 
                @if(isset($link['target'])) target="{{ $link['target'] }}" @endif
            >
                {{ $link['label'] }}
            </flux:navlist.item>
        @endforeach
    </flux:navlist>
@endif
