<section class="w-full">
    <x-page-heading>
        <x-slot:title>
            {{ $persona->nombre_completo }}
        </x-slot:title>
        <x-slot:subtitle>
            {{ __('personas.detalles_persona') }} - Cédula: {{ $persona->cedula }}
        </x-slot:subtitle>
        <x-slot:buttons>
            @can('update personas')
                <flux:button href="{{ route('admin.personas.edit', $persona) }}" variant="outline" icon="pencil">
                    {{ __('personas.editar') }}
                </flux:button>
            @endcan

            @can('create user from persona')
                @if(!$persona->user_id)
                    <flux:button wire:click="confirmCreateUser" variant="primary" icon="user-plus">
                        {{ __('personas.crear_usuario') }}
                    </flux:button>
                @endif
            @endcan

            @can('delete personas')
                <flux:button
                    wire:click="deletePersona"
                    variant="danger"
                    icon="trash"
                    wire:confirm="{{ __('personas.esta_seguro_eliminar') }}"
                >
                    {{ __('personas.eliminar') }}
                </flux:button>
            @endcan
        </x-slot:buttons>
    </x-page-heading>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Información Principal -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Datos Personales -->
            <div class="bg-white dark:bg-zinc-800 rounded-lg border border-zinc-200 dark:border-zinc-700 p-6">
                <h3 class="text-lg font-semibold mb-4">{{ __('personas.datos_personales') }}</h3>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-zinc-700 dark:text-zinc-300">{{ __('personas.nombres') }}</label>
                        <p class="mt-1 text-sm text-zinc-900 dark:text-zinc-100">{{ $persona->nombres }}</p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-zinc-700 dark:text-zinc-300">{{ __('personas.apellidos') }}</label>
                        <p class="mt-1 text-sm text-zinc-900 dark:text-zinc-100">{{ $persona->apellidos }}</p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-zinc-700 dark:text-zinc-300">{{ __('personas.cedula') }}</label>
                        <p class="mt-1 text-sm text-zinc-900 dark:text-zinc-100">{{ $persona->cedula }}</p>
                    </div>

                    @if($persona->fecha_nacimiento)
                    <div>
                        <label class="block text-sm font-medium text-zinc-700 dark:text-zinc-300">{{ __('personas.fecha_nacimiento') }}</label>
                        <p class="mt-1 text-sm text-zinc-900 dark:text-zinc-100">
                            {{ $persona->fecha_nacimiento->format('d/m/Y') }}
                            @if($persona->edad)
                                ({{ $persona->edad }} {{ __('personas.años') }})
                            @endif
                        </p>
                    </div>
                    @endif

                    @if($persona->genero)
                    <div>
                        <label class="block text-sm font-medium text-zinc-700 dark:text-zinc-300">{{ __('personas.genero') }}</label>
                        <p class="mt-1 text-sm text-zinc-900 dark:text-zinc-100">
                            {{ match($persona->genero) {
                                'M' => __('personas.masculino'),
                                'F' => __('personas.femenino'),
                                'O' => __('personas.otro'),
                                default => $persona->genero
                            } }}
                        </p>
                    </div>
                    @endif
                </div>
            </div>

            <!-- Información de Contacto -->
            <div class="bg-white dark:bg-zinc-800 rounded-lg border border-zinc-200 dark:border-zinc-700 p-6">
                <h3 class="text-lg font-semibold mb-4">{{ __('personas.informacion_contacto') }}</h3>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    @if($persona->telefono)
                    <div>
                        <label class="block text-sm font-medium text-zinc-700 dark:text-zinc-300">{{ __('personas.telefono') }}</label>
                        <p class="mt-1 text-sm text-zinc-900 dark:text-zinc-100">{{ $persona->telefono }}</p>
                    </div>
                    @endif

                    @if($persona->telefono_secundario)
                    <div>
                        <label class="block text-sm font-medium text-zinc-700 dark:text-zinc-300">{{ __('personas.telefono_secundario') }}</label>
                        <p class="mt-1 text-sm text-zinc-900 dark:text-zinc-100">{{ $persona->telefono_secundario }}</p>
                    </div>
                    @endif

                    @if($persona->email)
                    <div>
                        <label class="block text-sm font-medium text-zinc-700 dark:text-zinc-300">{{ __('personas.email') }}</label>
                        <p class="mt-1 text-sm text-zinc-900 dark:text-zinc-100">{{ $persona->email }}</p>
                    </div>
                    @endif
                </div>

                @if($persona->direccion)
                <div class="mt-4">
                    <label class="block text-sm font-medium text-zinc-700 dark:text-zinc-300">{{ __('personas.direccion') }}</label>
                    <p class="mt-1 text-sm text-zinc-900 dark:text-zinc-100">{{ $persona->direccion }}</p>
                </div>
                @endif
            </div>

            <!-- Ubicación Geográfica -->
            @if($persona->estado_id || $persona->municipio || $persona->parroquia)
            <div class="bg-white dark:bg-zinc-800 rounded-lg border border-zinc-200 dark:border-zinc-700 p-6">
                <h3 class="text-lg font-semibold mb-4">{{ __('personas.ubicacion') }}</h3>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    @if($persona->estado_id && $persona->estado)
                    <div>
                        <label class="block text-sm font-medium text-zinc-700 dark:text-zinc-300">{{ __('personas.estado') }}</label>
                        <p class="mt-1 text-sm text-zinc-900 dark:text-zinc-100">{{ $persona->estado->nombre }}</p>
                    </div>
                    @endif

                    @if($persona->municipio)
                    <div>
                        <label class="block text-sm font-medium text-zinc-700 dark:text-zinc-300">{{ __('personas.municipio') }}</label>
                        <p class="mt-1 text-sm text-zinc-900 dark:text-zinc-100">{{ $persona->municipio->nombre }}</p>
                    </div>
                    @endif

                    @if($persona->parroquia)
                    <div>
                        <label class="block text-sm font-medium text-zinc-700 dark:text-zinc-300">{{ __('personas.parroquia') }}</label>
                        <p class="mt-1 text-sm text-zinc-900 dark:text-zinc-100">{{ $persona->parroquia->nombre }}</p>
                    </div>
                    @endif
                </div>
            </div>
            @endif

            <!-- Información Electoral -->
            @if($persona->centroVotacion || $persona->mesa_votacion)
            <div class="bg-white dark:bg-zinc-800 rounded-lg border border-zinc-200 dark:border-zinc-700 p-6">
                <h3 class="text-lg font-semibold mb-4">{{ __('personas.informacion_electoral') }}</h3>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    @if($persona->centroVotacion)
                    <div>
                        <label class="block text-sm font-medium text-zinc-700 dark:text-zinc-300">{{ __('personas.centro_votacion') }}</label>
                        <p class="mt-1 text-sm text-zinc-900 dark:text-zinc-100">{{ $persona->centroVotacion->nombre }}</p>
                        @if($persona->centroVotacion->direccion)
                            <p class="text-xs text-zinc-500">{{ $persona->centroVotacion->direccion }}</p>
                        @endif
                    </div>
                    @endif

                    @if($persona->mesa_votacion)
                    <div>
                        <label class="block text-sm font-medium text-zinc-700 dark:text-zinc-300">{{ __('personas.mesa_votacion') }}</label>
                        <p class="mt-1 text-sm text-zinc-900 dark:text-zinc-100">{{ $persona->mesa_votacion }}</p>
                    </div>
                    @endif
                </div>
            </div>
            @endif

            <!-- Historial de Participación -->
            @if($persona->participaciones->count() > 0 || $persona->participacionesMovilizacion->count() > 0)
            <div class="bg-white dark:bg-zinc-800 rounded-lg border border-zinc-200 dark:border-zinc-700 p-6">
                <h3 class="text-lg font-semibold mb-4">{{ __('personas.historial_participacion') }}</h3>

                @if($persona->participaciones->count() > 0)
                <div class="mb-4">
                    <h4 class="font-medium mb-2">{{ __('personas.eventos_electorales') }}</h4>
                    <div class="space-y-2">
                        @foreach($persona->participaciones as $participacion)
                        <div class="flex justify-between items-center p-2 bg-zinc-50 dark:bg-zinc-700 rounded">
                            <span>{{ $participacion->eventoElectoral->nombre }}</span>
                            <flux:badge size="sm" :color="$participacion->confirmo_participacion ? 'green' : 'yellow'">
                                {{ $participacion->tipo_participacion }}
                            </flux:badge>
                        </div>
                        @endforeach
                    </div>
                </div>
                @endif

                @if($persona->participacionesMovilizacion->count() > 0)
                <div>
                    <h4 class="font-medium mb-2">{{ __('personas.movilizaciones') }}</h4>
                    <div class="space-y-2">
                        @foreach($persona->participacionesMovilizacion as $participacion)
                        <div class="flex justify-between items-center p-2 bg-zinc-50 dark:bg-zinc-700 rounded">
                            <span>{{ $participacion->movilizacion->nombre }}</span>
                            <flux:badge size="sm" :color="match($participacion->estado_participacion) {
                                'asistio' => 'green',
                                'confirmado' => 'blue',
                                'invitado' => 'yellow',
                                'no_asistio' => 'red',
                                default => 'zinc'
                            }">
                                {{ ucfirst($participacion->estado_participacion) }}
                            </flux:badge>
                        </div>
                        @endforeach
                    </div>
                </div>
                @endif
            </div>
            @endif
        </div>

        <!-- Panel Lateral -->
        <div class="space-y-6">
            <!-- Estado y Rol -->
            <div class="bg-white dark:bg-zinc-800 rounded-lg border border-zinc-200 dark:border-zinc-700 p-6">
                <h3 class="text-lg font-semibold mb-4">Estado y Rol</h3>

                <div class="space-y-3">
                    <div>
                        <label class="block text-sm font-medium text-zinc-700 dark:text-zinc-300">{{ __('personas.tipo_persona') }}</label>
                        <flux:badge
                            :color="match($persona->tipo_persona) {
                                'militante' => 'red',
                                'votante' => 'blue',
                                'simpatizante' => 'yellow',
                                default => 'zinc'
                            }"
                        >
                            {{ ucfirst($persona->tipo_persona) }}
                        </flux:badge>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-zinc-700 dark:text-zinc-300">{{ __('personas.estado_persona') }}</label>
                        <flux:badge
                            :color="match($persona->estado_persona) {
                                'activo' => 'green',
                                'inactivo' => 'yellow',
                                'suspendido' => 'red',
                                default => 'zinc'
                            }"
                        >
                            {{ ucfirst($persona->estado_persona) }}
                        </flux:badge>
                    </div>

                    @if($persona->es_lider_1x10)
                    <div>
                        <flux:badge color="blue">{{ __('personas.es_lider_1x10') }}</flux:badge>
                    </div>
                    @endif
                </div>
            </div>

            <!-- Usuario del Sistema -->
            @if($persona->user)
            <div class="bg-white dark:bg-zinc-800 rounded-lg border border-zinc-200 dark:border-zinc-700 p-6">
                <h3 class="text-lg font-semibold mb-4">Usuario del Sistema</h3>

                <div class="space-y-2">
                    <div>
                        <label class="block text-sm font-medium text-zinc-700 dark:text-zinc-300">Nombre de usuario</label>
                        <p class="text-sm text-zinc-900 dark:text-zinc-100">{{ $persona->user->username }}</p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-zinc-700 dark:text-zinc-300">Roles</label>
                        <div class="flex flex-wrap gap-1 mt-1">
                            @foreach($persona->user->roles as $role)
                                <flux:badge size="sm">{{ $role->name }}</flux:badge>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>
            @endif

            <!-- Liderazgo 1x10 -->
            @if($persona->es_lider_1x10 || $persona->liderAsignado)
            <div class="bg-white dark:bg-zinc-800 rounded-lg border border-zinc-200 dark:border-zinc-700 p-6">
                <h3 class="text-lg font-semibold mb-4">Liderazgo 1x10</h3>

                @if($persona->es_lider_1x10)
                <div class="mb-4">
                    <label class="block text-sm font-medium text-zinc-700 dark:text-zinc-300">{{ __('personas.personas_asignadas') }}</label>
                    <p class="text-2xl font-bold text-blue-600">{{ $persona->personasAsignadas->count() }}/10</p>
                    <p class="text-sm text-zinc-500">{{ $persona->espaciosDisponiblesLider() }} {{ __('personas.espacios_disponibles') }}</p>
                </div>

                @if($persona->personasAsignadas->count() > 0)
                <div>
                    <h4 class="font-medium mb-2">Personas Asignadas</h4>
                    <div class="space-y-1">
                        @foreach($persona->personasAsignadas as $asignada)
                        <div class="text-sm p-2 bg-zinc-50 dark:bg-zinc-700 rounded">
                            <a href="{{ route('admin.personas.show', $asignada) }}" class="text-blue-600 hover:text-blue-800">
                                {{ $asignada->nombre_completo }}
                            </a>
                        </div>
                        @endforeach
                    </div>
                </div>
                @endif
                @endif

                @if($persona->liderAsignado)
                <div>
                    <label class="block text-sm font-medium text-zinc-700 dark:text-zinc-300">{{ __('personas.lider_asignado') }}</label>
                    <a href="{{ route('admin.personas.show', $persona->liderAsignado) }}" class="text-blue-600 hover:text-blue-800">
                        {{ $persona->liderAsignado->nombre_completo }}
                    </a>
                </div>
                @endif
            </div>
            @endif

            <!-- Observaciones -->
            @if($persona->observaciones)
            <div class="bg-white dark:bg-zinc-800 rounded-lg border border-zinc-200 dark:border-zinc-700 p-6">
                <h3 class="text-lg font-semibold mb-4">{{ __('personas.observaciones') }}</h3>
                <p class="text-sm text-zinc-700 dark:text-zinc-300">{{ $persona->observaciones }}</p>
            </div>
            @endif
        </div>
    </div>

    <!-- Modal para crear usuario -->
    <flux:modal wire:model="showCreateUserModal" class="space-y-6">
        <div>
            <flux:heading size="lg">{{ __('personas.confirmar_crear_usuario') }}</flux:heading>
            <flux:subheading>{{ __('personas.crear_usuario_para_persona') }}</flux:subheading>
        </div>

        <div class="space-y-4">
            <p class="text-sm text-zinc-600 dark:text-zinc-400">
                Se creará un usuario del sistema para <strong>{{ $persona->nombre_completo }}</strong>
                con el correo electrónico <strong>{{ $persona->email }}</strong>.
            </p>

            <p class="text-sm text-zinc-600 dark:text-zinc-400">
                {{ __('personas.se_enviaran_credenciales') }}
            </p>
        </div>

        <div class="flex justify-end space-x-2">
            <flux:button wire:click="$set('showCreateUserModal', false)" variant="outline">
                {{ __('personas.cancelar') }}
            </flux:button>

            <flux:button wire:click="createUser" variant="primary">
                {{ __('personas.crear_usuario') }}
            </flux:button>
        </div>
    </flux:modal>
</section>
