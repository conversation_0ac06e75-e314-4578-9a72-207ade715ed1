<section class="w-full">
    <x-page-heading>
        <x-slot:title>
            {{ __('personas.title') }}
        </x-slot:title>
        <x-slot:subtitle>
            {{ __('personas.title_description') }}
        </x-slot:subtitle>
        <x-slot:buttons>
            <flux:button href="{{ route('admin.personas.busqueda') }}" variant="outline" icon="magnifying-glass">
                {{ __('personas.busqueda_avanzada') }}
            </flux:button>

            @can('create personas')
                <flux:button href="{{ route('admin.personas.create') }}" variant="primary" icon="plus">
                    {{ __('personas.agregar_persona') }}
                </flux:button>
            @endcan

            @can('export personas')
                <flux:button wire:click="exportPersonas" variant="outline">
                    {{ __('personas.exportar') }}
                </flux:button>
            @endcan
        </x-slot:buttons>
    </x-page-heading>

    <!-- Pestañas de navegación -->
    <div class="mb-6">
        <flux:tabs wire:model.live="activeTab">
            <flux:tab name="todos" label="{{ __('personas.todos') }} ({{ $stats['total'] }})" />
            <flux:tab name="militantes" label="{{ __('personas.militantes') }} ({{ $stats['militantes'] }})" />
            <flux:tab name="votantes" label="{{ __('personas.votantes') }} ({{ $stats['votantes'] }})" />
            <flux:tab name="simpatizantes" label="{{ __('personas.simpatizantes') }} ({{ $stats['simpatizantes'] }})" />
            <flux:tab name="lideres" label="{{ __('personas.lideres_1x10') }} ({{ $stats['lideres'] }})" />
        </flux:tabs>
    </div>

    <!-- Filtros -->
    <div class="mb-6 p-4 bg-white dark:bg-zinc-800 rounded-lg border border-zinc-200 dark:border-zinc-700">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <!-- Búsqueda por texto -->
            <div>
                <flux:input
                    wire:model.live.debounce.300ms="search"
                    placeholder="{{ __('personas.buscar_por_nombre') }}"
                    icon="magnifying-glass"
                />
            </div>

            <!-- Filtro por estado geográfico -->
            <div>
                <flux:select wire:model.live="estadoId" placeholder="{{ __('personas.seleccionar_estado') }}">
                    <flux:select.option value="">{{ __('personas.seleccionar_estado') }}</flux:select.option>
                    @foreach($estados as $estado)
                        <flux:select.option value="{{ $estado->id }}">{{ $estado->nombre }}</flux:select.option>
                    @endforeach
                </flux:select>
            </div>

            <!-- Filtro por municipio -->
            <div>
                <flux:select wire:model.live="municipioId" placeholder="{{ __('personas.seleccionar_municipio') }}" :disabled="!$estadoId">
                    <flux:select.option value="">{{ __('personas.seleccionar_municipio') }}</flux:select.option>
                    @foreach($municipios as $municipio)
                        <flux:select.option value="{{ $municipio->id }}">{{ $municipio->nombre }}</flux:select.option>
                    @endforeach
                </flux:select>
            </div>

            <!-- Filtro por parroquia -->
            <div>
                <flux:select wire:model.live="parroquiaId" placeholder="{{ __('personas.seleccionar_parroquia') }}" :disabled="!$municipioId">
                    <flux:select.option value="">{{ __('personas.seleccionar_parroquia') }}</flux:select.option>
                    @foreach($parroquias as $parroquia)
                        <flux:select.option value="{{ $parroquia->id }}">{{ $parroquia->nombre }}</flux:select.option>
                    @endforeach
                </flux:select>
            </div>

            <!-- Filtro por estado de persona -->
            <div>
                <flux:select wire:model.live="estadoPersona" placeholder="{{ __('personas.filtrar_por_estado') }}">
                    <flux:select.option value="">{{ __('personas.filtrar_por_estado') }}</flux:select.option>
                    <flux:select.option value="activo">{{ __('personas.activo') }}</flux:select.option>
                    <flux:select.option value="inactivo">{{ __('personas.inactivo') }}</flux:select.option>
                    <flux:select.option value="suspendido">{{ __('personas.suspendido') }}</flux:select.option>
                </flux:select>
            </div>

            <!-- Filtro por líder 1x10 -->
            @if($activeTab === 'todos')
            <div>
                <flux:select wire:model.live="esLider" placeholder="{{ __('personas.filtrar_por_liderazgo') }}">
                    <flux:select.option value="">{{ __('personas.todos') }}</flux:select.option>
                    <flux:select.option value="1">{{ __('personas.solo_lideres_1x10') }}</flux:select.option>
                    <flux:select.option value="0">{{ __('personas.no_lideres') }}</flux:select.option>
                </flux:select>
            </div>
            @endif

            <!-- Botón limpiar filtros -->
            <div class="flex items-end">
                <flux:button wire:click="clearFilters" variant="outline" size="sm">
                    {{ __('personas.limpiar_filtros') }}
                </flux:button>
            </div>
        </div>
    </div>

    <!-- Tabla de personas -->
    <x-table>
        <x-slot:head>
            <x-table.row>
                <x-table.heading>{{ __('personas.cedula') }}</x-table.heading>
                <x-table.heading>{{ __('personas.nombre_completo') }}</x-table.heading>
                <x-table.heading>{{ __('personas.telefono') }}</x-table.heading>
                <x-table.heading>{{ __('personas.ubicacion') }}</x-table.heading>
                <x-table.heading>{{ __('personas.tipo_persona') }}</x-table.heading>
                <x-table.heading>{{ __('personas.estado_persona') }}</x-table.heading>
                <x-table.heading class="text-right">{{ __('global.actions') }}</x-table.heading>
            </x-table.row>
        </x-slot:head>
        <x-slot:body>
            @forelse($personas as $persona)
                <x-table.row wire:key="persona-{{ $persona->id }}">
                    <x-table.cell>
                        <div class="font-medium">{{ $persona->cedula }}</div>
                        @if($persona->es_lider_1x10)
                            <flux:badge size="sm" color="blue">Líder 1x10</flux:badge>
                        @endif
                    </x-table.cell>
                    <x-table.cell>
                        <div class="font-medium">{{ $persona->nombre_completo }}</div>
                        @if($persona->email)
                            <div class="text-sm text-zinc-500">{{ $persona->email }}</div>
                        @endif
                        @if($persona->user)
                            <flux:badge size="sm" color="green">Tiene usuario</flux:badge>
                        @endif
                    </x-table.cell>
                    <x-table.cell>
                        @if($persona->telefono)
                            <div>{{ $persona->telefono }}</div>
                        @endif
                        @if($persona->telefono_secundario)
                            <div class="text-sm text-zinc-500">{{ $persona->telefono_secundario }}</div>
                        @endif
                    </x-table.cell>
                    <x-table.cell>

                    </x-table.cell>
                    <x-table.cell>
                        <flux:badge
                            size="sm"
                            :color="match($persona->tipo_persona) {
                                'militante' => 'red',
                                'votante' => 'blue',
                                'simpatizante' => 'yellow',
                                default => 'zinc'
                            }"
                        >
                            {{ ucfirst($persona->tipo_persona) }}
                        </flux:badge>
                        @if($persona->liderAsignado)
                            <div class="text-xs text-zinc-500 mt-1">
                                Líder: {{ $persona->liderAsignado->nombre_completo }}
                            </div>
                        @endif
                    </x-table.cell>
                    <x-table.cell>
                        <flux:badge
                            size="sm"
                            :color="match($persona->estado_persona) {
                                'activo' => 'green',
                                'inactivo' => 'yellow',
                                'suspendido' => 'red',
                                default => 'zinc'
                            }"
                        >
                            {{ ucfirst($persona->estado_persona) }}
                        </flux:badge>
                    </x-table.cell>
                    <x-table.cell class="gap-2 flex justify-end">
                        <flux:button href="{{ route('admin.personas.show', $persona) }}" size="sm" variant="ghost">
                            {{ __('personas.ver_detalles') }}
                        </flux:button>

                        @can('update personas')
                            <flux:button href="{{ route('admin.personas.edit', $persona) }}" size="sm">
                                {{ __('personas.editar') }}
                            </flux:button>
                        @endcan

                        @can('delete personas')
                            <flux:button
                                wire:click="deletePersona({{ $persona->id }})"
                                size="sm"
                                variant="danger"
                                wire:confirm="{{ __('personas.esta_seguro_eliminar') }}"
                            >
                                {{ __('personas.eliminar') }}
                            </flux:button>
                        @endcan
                    </x-table.cell>
                </x-table.row>
            @empty
                <x-table.row>
                    <x-table.cell colspan="7" class="text-center py-8">
                        <div class="text-zinc-500">
                            No se encontraron personas con los filtros aplicados.
                        </div>
                    </x-table.cell>
                </x-table.row>
            @endforelse
        </x-slot:body>
    </x-table>

    <!-- Paginación -->
    <div class="mt-6">
        {{ $personas->links() }}
    </div>
</section>
