<section class="w-full">
    <x-page-heading>
        <x-slot:title>
            {{ __('personas.busqueda_avanzada') }}
        </x-slot:title>
        <x-slot:subtitle>
            Utilice múltiples criterios para encontrar personas específicas en el sistema
        </x-slot:subtitle>
        <x-slot:buttons>
            @if($mostrarResultados)
                @can('export personas')
                    <flux:button wire:click="exportarResultados" variant="outline" icon="download">
                        {{ __('personas.exportar') }}
                    </flux:button>
                @endcan
            @endif
        </x-slot:buttons>
    </x-page-heading>

    <!-- Formulario de búsqueda -->
    <div class="bg-white dark:bg-zinc-800 rounded-lg border border-zinc-200 dark:border-zinc-700 p-6 mb-6">
        <h3 class="text-lg font-semibold mb-6">Criterios de Búsqueda</h3>

        <div class="space-y-6">
            <!-- Datos Personales -->
            <div>
                <h4 class="font-medium mb-3 text-zinc-700 dark:text-zinc-300">Datos Personales</h4>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <flux:input
                        wire:model.blur="nombres"
                        label="{{ __('personas.nombres') }}"
                        placeholder="Buscar por nombres"
                    />

                    <flux:input
                        wire:model.blur="apellidos"
                        label="{{ __('personas.apellidos') }}"
                        placeholder="Buscar por apellidos"
                    />

                    <flux:input
                        wire:model.blur="cedula"
                        label="{{ __('personas.cedula') }}"
                        placeholder="Ej: V-12345678"
                    />
                </div>
            </div>

            <!-- Información de Contacto -->
            <div>
                <h4 class="font-medium mb-3 text-zinc-700 dark:text-zinc-300">Información de Contacto</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <flux:input
                        wire:model.blur="email"
                        label="{{ __('personas.email') }}"
                        placeholder="Buscar por correo electrónico"
                    />

                    <flux:input
                        wire:model.blur="telefono"
                        label="{{ __('personas.telefono') }}"
                        placeholder="Buscar por teléfono"
                    />
                </div>
            </div>

            <!-- Ubicación Geográfica -->
            <div>
                <h4 class="font-medium mb-3 text-zinc-700 dark:text-zinc-300">Ubicación Geográfica</h4>
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <flux:select wire:model.live="estado_id" label="{{ __('personas.estado') }}" placeholder="{{ __('personas.seleccionar_estado') }}">
                        @foreach($estados as $estado)
                            <flux:select.option value="{{ $estado->id }}">{{ $estado->nombre }}</flux:select.option>
                        @endforeach
                    </flux:select>

                    <flux:select wire:model.live="municipio_id" label="{{ __('personas.municipio') }}" placeholder="{{ __('personas.seleccionar_municipio') }}" :disabled="!$estado_id">
                        @foreach($municipios as $municipio)
                            <flux:select.option value="{{ $municipio->id }}">{{ $municipio->nombre }}</flux:select.option>
                        @endforeach
                    </flux:select>

                    <flux:select wire:model.live="parroquia_id" label="{{ __('personas.parroquia') }}" placeholder="{{ __('personas.seleccionar_parroquia') }}" :disabled="!$municipio_id">
                        @foreach($parroquias as $parroquia)
                            <flux:select.option value="{{ $parroquia->id }}">{{ $parroquia->nombre }}</flux:select.option>
                        @endforeach
                    </flux:select>

                    <flux:select wire:model.live="centro_votacion_id" label="{{ __('personas.centro_votacion') }}" placeholder="{{ __('personas.seleccionar_centro') }}" :disabled="!$parroquia_id">
                        @foreach($centrosVotacion as $centro)
                            <flux:select.option value="{{ $centro->id }}">{{ $centro->nombre }}</flux:select.option>
                        @endforeach
                    </flux:select>
                </div>
            </div>

            <!-- Clasificación -->
            <div>
                <h4 class="font-medium mb-3 text-zinc-700 dark:text-zinc-300">Clasificación</h4>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <flux:select wire:model.live="tipo_persona" label="{{ __('personas.tipo_persona') }}" placeholder="Todos los tipos">
                        <flux:select.option value="militante">{{ __('personas.militante') }}</flux:select.option>
                        <flux:select.option value="votante">{{ __('personas.votante') }}</flux:select.option>
                        <flux:select.option value="simpatizante">{{ __('personas.simpatizante') }}</flux:select.option>
                    </flux:select>

                    <flux:select wire:model.live="estado_persona" label="{{ __('personas.estado_persona') }}" placeholder="Todos los estados">
                        <flux:select.option value="activo">{{ __('personas.activo') }}</flux:select.option>
                        <flux:select.option value="inactivo">{{ __('personas.inactivo') }}</flux:select.option>
                        <flux:select.option value="suspendido">{{ __('personas.suspendido') }}</flux:select.option>
                    </flux:select>

                    <flux:select wire:model.live="es_lider_1x10" label="Liderazgo 1x10" placeholder="Todos">
                        <flux:select.option value="1">Solo líderes 1x10</flux:select.option>
                        <flux:select.option value="0">No líderes</flux:select.option>
                    </flux:select>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                    <flux:select wire:model.live="tiene_lider" label="Asignación de Líder" placeholder="Todos">
                        <flux:select.option value="1">Con líder asignado</flux:select.option>
                        <flux:select.option value="0">Sin líder asignado</flux:select.option>
                    </flux:select>

                    <flux:select wire:model.live="tiene_usuario" label="Usuario del Sistema" placeholder="Todos">
                        <flux:select.option value="1">Con usuario del sistema</flux:select.option>
                        <flux:select.option value="0">Sin usuario del sistema</flux:select.option>
                    </flux:select>
                </div>
            </div>

            <!-- Filtros de Fecha y Edad -->
            <div>
                <h4 class="font-medium mb-3 text-zinc-700 dark:text-zinc-300">Fechas y Edad</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Fecha de Nacimiento -->
                    <div>
                        <label class="block text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-2">Fecha de Nacimiento</label>
                        <div class="grid grid-cols-2 gap-2">
                            <flux:input
                                wire:model.blur="fecha_nacimiento_desde"
                                type="date"
                                placeholder="Desde"
                            />
                            <flux:input
                                wire:model.blur="fecha_nacimiento_hasta"
                                type="date"
                                placeholder="Hasta"
                            />
                        </div>
                    </div>

                    <!-- Fecha de Registro -->
                    <div>
                        <label class="block text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-2">Fecha de Registro</label>
                        <div class="grid grid-cols-2 gap-2">
                            <flux:input
                                wire:model.blur="fecha_registro_desde"
                                type="date"
                                placeholder="Desde"
                            />
                            <flux:input
                                wire:model.blur="fecha_registro_hasta"
                                type="date"
                                placeholder="Hasta"
                            />
                        </div>
                    </div>
                </div>

                <!-- Rango de Edad -->
                <div class="mt-4">
                    <label class="block text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-2">Rango de Edad</label>
                    <div class="grid grid-cols-2 gap-4 max-w-md">
                        <flux:input
                            wire:model.blur="edad_minima"
                            type="number"
                            label="Edad mínima"
                            placeholder="18"
                            min="0"
                            max="120"
                        />
                        <flux:input
                            wire:model.blur="edad_maxima"
                            type="number"
                            label="Edad máxima"
                            placeholder="65"
                            min="0"
                            max="120"
                        />
                    </div>
                </div>
            </div>
        </div>

        <!-- Botones de acción -->
        <div class="flex justify-end space-x-4 mt-6 pt-6 border-t border-zinc-200 dark:border-zinc-700">
            <flux:button wire:click="limpiarFiltros" variant="outline">
                {{ __('personas.limpiar_filtros') }}
            </flux:button>

            <flux:button wire:click="buscar" variant="primary" icon="magnifying-glass">
                {{ __('personas.buscar') }}
            </flux:button>
        </div>
    </div>

    <!-- Resultados de búsqueda -->
    @if($mostrarResultados)
        <div class="bg-white dark:bg-zinc-800 rounded-lg border border-zinc-200 dark:border-zinc-700">
            <div class="p-6 border-b border-zinc-200 dark:border-zinc-700">
                <h3 class="text-lg font-semibold">Resultados de Búsqueda</h3>
                <p class="text-sm text-zinc-600 dark:text-zinc-400 mt-1">
                    Se encontraron {{ $personas->total() }} persona(s) que coinciden con los criterios
                </p>
            </div>

            @if($personas->count() > 0)
                <!-- Tabla de resultados -->
                <div class="overflow-x-auto">
                    <table class="w-full">
                        <thead class="bg-zinc-50 dark:bg-zinc-700">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-zinc-500 dark:text-zinc-300 uppercase tracking-wider">
                                    Persona
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-zinc-500 dark:text-zinc-300 uppercase tracking-wider">
                                    Contacto
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-zinc-500 dark:text-zinc-300 uppercase tracking-wider">
                                    Ubicación
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-zinc-500 dark:text-zinc-300 uppercase tracking-wider">
                                    Clasificación
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-zinc-500 dark:text-zinc-300 uppercase tracking-wider">
                                    Estado
                                </th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-zinc-500 dark:text-zinc-300 uppercase tracking-wider">
                                    Acciones
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white dark:bg-zinc-800 divide-y divide-zinc-200 dark:divide-zinc-700">
                            @foreach($personas as $persona)
                                <tr class="hover:bg-zinc-50 dark:hover:bg-zinc-700">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div>
                                            <div class="font-medium text-zinc-900 dark:text-zinc-100">
                                                {{ $persona->nombre_completo }}
                                            </div>
                                            <div class="text-sm text-zinc-500">
                                                {{ $persona->cedula }}
                                                @if($persona->edad)
                                                    - {{ $persona->edad }} años
                                                @endif
                                            </div>
                                            @if($persona->es_lider_1x10)
                                                <flux:badge size="sm" color="blue">Líder 1x10</flux:badge>
                                            @endif
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        @if($persona->telefono)
                                            <div class="text-sm text-zinc-900 dark:text-zinc-100">{{ $persona->telefono }}</div>
                                        @endif
                                        @if($persona->email)
                                            <div class="text-sm text-zinc-500">{{ $persona->email }}</div>
                                        @endif
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        @if($persona->estado_id && $persona->estado)
                                            <div class="text-sm text-zinc-900 dark:text-zinc-100">{{ $persona->estado->nombre }}</div>
                                        @endif
                                        @if($persona->municipio)
                                            <div class="text-sm text-zinc-500">{{ $persona->municipio->nombre }}</div>
                                        @endif
                                        @if(!$persona->estado_id && !$persona->municipio)
                                            <span class="text-zinc-400 text-sm">Sin ubicación</span>
                                        @endif
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <flux:badge
                                            size="sm"
                                            :color="match($persona->tipo_persona) {
                                                'militante' => 'red',
                                                'votante' => 'blue',
                                                'simpatizante' => 'yellow',
                                                default => 'zinc'
                                            }"
                                        >
                                            {{ ucfirst($persona->tipo_persona) }}
                                        </flux:badge>
                                        @if($persona->user)
                                            <flux:badge size="sm" color="green" class="ml-1">Usuario</flux:badge>
                                        @endif
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <flux:badge
                                            size="sm"
                                            :color="match($persona->estado_persona) {
                                                'activo' => 'green',
                                                'inactivo' => 'yellow',
                                                'suspendido' => 'red',
                                                default => 'zinc'
                                            }"
                                        >
                                            {{ ucfirst($persona->estado_persona) }}
                                        </flux:badge>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                        <div class="flex justify-end space-x-2">
                                            <flux:button href="{{ route('admin.personas.show', $persona) }}" size="sm" variant="ghost">
                                                Ver
                                            </flux:button>
                                            @can('update personas')
                                                <flux:button href="{{ route('admin.personas.edit', $persona) }}" size="sm">
                                                    Editar
                                                </flux:button>
                                            @endcan
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <!-- Paginación -->
                <div class="px-6 py-4 border-t border-zinc-200 dark:border-zinc-700">
                    {{ $personas->links() }}
                </div>
            @else
                <div class="p-8 text-center">
                    <div class="text-zinc-500 dark:text-zinc-400">
                        No se encontraron personas que coincidan con los criterios de búsqueda.
                    </div>
                </div>
            @endif
        </div>
    @endif
</section>
