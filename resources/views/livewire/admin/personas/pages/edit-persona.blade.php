<section class="w-full">
    <x-page-heading>
        <x-slot:title>
            {{ __('personas.editar_persona') }}
        </x-slot:title>
        <x-slot:subtitle>
            Editando: {{ $persona->nombre_completo }} - Cédula: {{ $persona->cedula }}
        </x-slot:subtitle>
    </x-page-heading>

    <x-form wire:submit="updatePersona" class="space-y-8">
        <!-- Datos Personales -->
        <div class="bg-white dark:bg-zinc-800 rounded-lg border border-zinc-200 dark:border-zinc-700 p-6">
            <h3 class="text-lg font-semibold mb-4">{{ __('personas.datos_personales') }}</h3>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <flux:input
                    wire:model.blur="nombres"
                    label="{{ __('personas.nombres') }}"
                    placeholder="Ingrese los nombres"
                    required
                />

                <flux:input
                    wire:model.blur="apellidos"
                    label="{{ __('personas.apellidos') }}"
                    placeholder="Ingrese los apellidos"
                    required
                />

                <flux:input
                    wire:model.blur="cedula"
                    label="{{ __('personas.cedula') }}"
                    placeholder="Ej: V-12345678"
                    required
                />

                <flux:input
                    wire:model.blur="fecha_nacimiento"
                    label="{{ __('personas.fecha_nacimiento') }}"
                    type="date"
                />

                <flux:select wire:model.live="genero" label="{{ __('personas.genero') }}" placeholder="Seleccionar género">
                    <flux:select.option value="M">{{ __('personas.masculino') }}</flux:select.option>
                    <flux:select.option value="F">{{ __('personas.femenino') }}</flux:select.option>
                    <flux:select.option value="O">{{ __('personas.otro') }}</flux:select.option>
                </flux:select>
            </div>
        </div>

        <!-- Información de Contacto -->
        <div class="bg-white dark:bg-zinc-800 rounded-lg border border-zinc-200 dark:border-zinc-700 p-6">
            <h3 class="text-lg font-semibold mb-4">{{ __('personas.informacion_contacto') }}</h3>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <flux:input
                    wire:model.blur="telefono"
                    label="{{ __('personas.telefono') }}"
                    placeholder="Ej: 0414-1234567"
                />

                <flux:input
                    wire:model.blur="telefono_secundario"
                    label="{{ __('personas.telefono_secundario') }}"
                    placeholder="Ej: 0212-1234567"
                />

                <flux:input
                    wire:model.blur="email"
                    label="{{ __('personas.email') }}"
                    type="email"
                    placeholder="<EMAIL>"
                />
            </div>

            <div class="mt-4">
                <flux:textarea
                    wire:model.blur="direccion"
                    label="{{ __('personas.direccion') }}"
                    placeholder="Dirección completa de residencia"
                    rows="3"
                />
            </div>
        </div>

        <!-- Ubicación Geográfica -->
        <div class="bg-white dark:bg-zinc-800 rounded-lg border border-zinc-200 dark:border-zinc-700 p-6">
            <h3 class="text-lg font-semibold mb-4">{{ __('personas.ubicacion') }}</h3>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <flux:select wire:model.live="estado_id" label="{{ __('personas.estado') }}" placeholder="{{ __('personas.seleccionar_estado') }}">
                    @foreach($estados as $estado)
                        <flux:select.option value="{{ $estado->id }}">{{ $estado->nombre }}</flux:select.option>
                    @endforeach
                </flux:select>

                <flux:select wire:model.live="municipio_id" label="{{ __('personas.municipio') }}" placeholder="{{ __('personas.seleccionar_municipio') }}" :disabled="!$estado_id">
                    @foreach($municipios as $municipio)
                        <flux:select.option value="{{ $municipio->id }}">{{ $municipio->nombre }}</flux:select.option>
                    @endforeach
                </flux:select>

                <flux:select wire:model.live="parroquia_id" label="{{ __('personas.parroquia') }}" placeholder="{{ __('personas.seleccionar_parroquia') }}" :disabled="!$municipio_id">
                    @foreach($parroquias as $parroquia)
                        <flux:select.option value="{{ $parroquia->id }}">{{ $parroquia->nombre }}</flux:select.option>
                    @endforeach
                </flux:select>
            </div>
        </div>

        <!-- Información Electoral -->
        <div class="bg-white dark:bg-zinc-800 rounded-lg border border-zinc-200 dark:border-zinc-700 p-6">
            <h3 class="text-lg font-semibold mb-4">{{ __('personas.informacion_electoral') }}</h3>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <flux:select wire:model.live="centro_votacion_id" label="{{ __('personas.centro_votacion') }}" placeholder="{{ __('personas.seleccionar_centro') }}" :disabled="!$parroquia_id">
                    @foreach($centrosVotacion as $centro)
                        <flux:select.option value="{{ $centro->id }}">{{ $centro->nombre }}</flux:select.option>
                    @endforeach
                </flux:select>

                <flux:input
                    wire:model.blur="mesa_votacion"
                    label="{{ __('personas.mesa_votacion') }}"
                    placeholder="Ej: 001"
                />
            </div>
        </div>

        <!-- Rol en el Sistema -->
        <div class="bg-white dark:bg-zinc-800 rounded-lg border border-zinc-200 dark:border-zinc-700 p-6">
            <h3 class="text-lg font-semibold mb-4">{{ __('personas.rol_sistema') }}</h3>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <flux:select wire:model.live="tipo_persona" label="{{ __('personas.tipo_persona') }}" required>
                    <flux:select.option value="militante">{{ __('personas.militante') }}</flux:select.option>
                    <flux:select.option value="votante">{{ __('personas.votante') }}</flux:select.option>
                    <flux:select.option value="simpatizante">{{ __('personas.simpatizante') }}</flux:select.option>
                </flux:select>

                <div class="flex items-center space-x-2">
                    <flux:checkbox wire:model.live="es_lider_1x10" label="{{ __('personas.es_lider_1x10') }}" />
                    @if($persona->es_lider_1x10 && $persona->personasAsignadas()->count() > 0)
                        <span class="text-sm text-amber-600">
                            (Tiene {{ $persona->personasAsignadas()->count() }} personas asignadas)
                        </span>
                    @endif
                </div>
            </div>

            @if(!$es_lider_1x10)
            <div class="mt-4">
                <flux:select wire:model.live="lider_asignado_id" label="{{ __('personas.lider_asignado') }}" placeholder="{{ __('personas.seleccionar_lider') }}">
                    @foreach($lideresDisponibles as $lider)
                        <flux:select.option value="{{ $lider->id }}">
                            {{ $lider->nombre_completo }} ({{ $lider->espaciosDisponiblesLider() }} espacios disponibles)
                        </flux:select.option>
                    @endforeach
                </flux:select>
            </div>
            @endif
        </div>

        <!-- Estado y Observaciones -->
        <div class="bg-white dark:bg-zinc-800 rounded-lg border border-zinc-200 dark:border-zinc-700 p-6">
            <h3 class="text-lg font-semibold mb-4">Estado y Observaciones</h3>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <flux:select wire:model.live="estado" label="{{ __('personas.estado_persona') }}" required>
                    <flux:select.option value="activo">{{ __('personas.activo') }}</flux:select.option>
                    <flux:select.option value="inactivo">{{ __('personas.inactivo') }}</flux:select.option>
                    <flux:select.option value="suspendido">{{ __('personas.suspendido') }}</flux:select.option>
                </flux:select>
            </div>

            <div class="mt-4">
                <flux:textarea
                    wire:model.blur="observaciones"
                    label="{{ __('personas.observaciones') }}"
                    placeholder="Observaciones adicionales sobre la persona"
                    rows="3"
                />
            </div>
        </div>

        <!-- Información adicional si es líder -->
        @if($persona->es_lider_1x10 && $persona->personasAsignadas()->count() > 0)
        <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-700 p-6">
            <h3 class="text-lg font-semibold mb-4 text-blue-800 dark:text-blue-200">Personas Asignadas como Líder</h3>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                @foreach($persona->personasAsignadas as $asignada)
                <div class="p-3 bg-white dark:bg-zinc-800 rounded border">
                    <div class="font-medium">{{ $asignada->nombre_completo }}</div>
                    <div class="text-sm text-zinc-500">{{ $asignada->cedula }}</div>
                    <div class="text-sm text-zinc-500">{{ ucfirst($asignada->tipo_persona) }}</div>
                </div>
                @endforeach
            </div>

            @if(!$es_lider_1x10)
            <div class="mt-4 p-3 bg-amber-100 dark:bg-amber-900/20 rounded border border-amber-200 dark:border-amber-700">
                <p class="text-sm text-amber-800 dark:text-amber-200">
                    <strong>Advertencia:</strong> Si quita el rol de líder 1x10, estas personas quedarán sin líder asignado.
                </p>
            </div>
            @endif
        </div>
        @endif

        <!-- Botones de acción -->
        <div class="flex justify-end space-x-4">
            <flux:button href="{{ route('admin.personas.show', $persona) }}" variant="outline">
                {{ __('personas.cancelar') }}
            </flux:button>

            <flux:button type="submit" variant="primary" icon="save">
                {{ __('personas.actualizar') }}
            </flux:button>
        </div>
    </x-form>
</section>
