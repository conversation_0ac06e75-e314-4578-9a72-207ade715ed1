<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Configuración del Menú del Sidebar
    |--------------------------------------------------------------------------
    |
    | Este archivo contiene la configuración del menú del sidebar de la
    | aplicación. Permite organizar los elementos del menú de forma
    | estructurada y facilita el mantenimiento.
    |
    */

    'items' => [
        [
            'type' => 'group',
            'heading' => 'Platform',
            'class' => 'grid',
            'items' => [
                [
                    'type' => 'item',
                    'icon' => 'home',
                    'route' => 'admin.index',
                    'label' => 'Dashboard',
                    'current_check' => 'admin.index'
                ]
            ]
        ],

        [
            'type' => 'group',
            'heading' => 'Users',
            'class' => 'grid',
            'permission' => ['view users', 'view roles', 'view permissions'],
            'permission_type' => 'any',
            'items' => [
                [
                    'type' => 'item',
                    'icon' => 'user',
                    'route' => 'admin.users.index',
                    'label_key' => 'users.title',
                    'current_check' => 'admin.users.*',
                    'permission' => 'view users'
                ],
                [
                    'type' => 'item',
                    'icon' => 'shield-user',
                    'route' => 'admin.roles.index',
                    'label_key' => 'roles.title',
                    'current_check' => 'admin.roles.*',
                    'permission' => 'view roles'
                ],
                [
                    'type' => 'item',
                    'icon' => 'shield-check',
                    'route' => 'admin.permissions.index',
                    'label_key' => 'permissions.title',
                    'current_check' => 'admin.permissions.*',
                    'permission' => 'view permissions'
                ]
            ]
        ],

        [
            'type' => 'group',
            'heading_key' => 'personas.title',
            'class' => 'grid',
            'permission' => 'view personas',
            'items' => [
                [
                    'type' => 'item',
                    'icon' => 'users',
                    'route' => 'admin.personas.index',
                    'label_key' => 'personas.listado',
                    'current_check' => 'admin.personas.index',
                    'permission' => 'view personas'
                ],
                [
                    'type' => 'item',
                    'icon' => 'magnifying-glass',
                    'route' => 'admin.personas.busqueda',
                    'label_key' => 'personas.busqueda_avanzada',
                    'current_check' => 'admin.personas.busqueda',
                    'permission' => 'view personas'
                ],
                [
                    'type' => 'item',
                    'icon' => 'plus',
                    'route' => 'admin.personas.create',
                    'label_key' => 'personas.agregar_persona',
                    'current_check' => 'admin.personas.create',
                    'permission' => 'create personas'
                ]
            ]
        ],

        // Aquí se pueden agregar más grupos de menú según sea necesario
        // Ejemplo para futuras funcionalidades:
        /*
        [
            'type' => 'group',
            'heading' => 'Eventos Electorales',
            'class' => 'grid',
            'permission' => 'view eventos',
            'items' => [
                [
                    'type' => 'item',
                    'icon' => 'calendar',
                    'route' => 'admin.eventos.index',
                    'label_key' => 'eventos.listado',
                    'current_check' => 'admin.eventos.*',
                    'permission' => 'view eventos'
                ]
            ]
        ],
        */
    ],

    /*
    |--------------------------------------------------------------------------
    | Configuración de Elementos Especiales
    |--------------------------------------------------------------------------
    |
    | Configuración para elementos especiales del menú como favoritos,
    | enlaces externos, etc.
    |
    */

    'favorites' => [
        'enabled' => false, // Cambiar a true para habilitar la sección de favoritos
        'expandable' => true,
        'heading' => 'Favorites',
        'class' => 'lg:grid',
        'items' => [
            [
                'label' => 'Marketing site',
                'url' => '#'
            ],
            [
                'label' => 'Android app',
                'url' => '#'
            ],
            [
                'label' => 'Brand guidelines',
                'url' => '#'
            ]
        ]
    ],

    /*
    |--------------------------------------------------------------------------
    | Configuración de Enlaces Externos
    |--------------------------------------------------------------------------
    |
    | Enlaces a documentación, repositorios, etc.
    |
    */

    'external_links' => [
        'enabled' => false, // Cambiar a true para habilitar enlaces externos
        'items' => [
            [
                'icon' => 'folder-git-2',
                'label' => 'Repository',
                'url' => 'https://github.com/laravel/livewire-starter-kit',
                'target' => '_blank'
            ],
            [
                'icon' => 'book-open-text',
                'label' => 'Documentation',
                'url' => 'https://laravel.com/docs/starter-kits',
                'target' => '_blank'
            ]
        ]
    ]
];
