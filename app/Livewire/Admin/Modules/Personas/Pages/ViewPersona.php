<?php

namespace App\Livewire\Admin\Personas\Pages;

use App\Models\Persona;
use App\Models\User;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Jantinnerezo\LivewireAlert\LivewireAlert;
use Livewire\Attributes\Layout;
use Livewire\Component;

class ViewPersona extends Component
{
    use LivewireAlert;

    public Persona $persona;
    public bool $showCreateUserModal = false;

    public function mount(Persona $persona): void
    {
        $this->authorize('view personas');
        $this->persona = $persona->load([
            'estado',
            'municipio',
            'parroquia',
            'centroVotacion',
            'liderAsignado',
            'personasAsignadas',
            'user',
            'participaciones.eventoElectoral',
            'participacionesMovilizacion.movilizacion'
        ]);
    }

    public function confirmCreateUser(): void
    {
        $this->authorize('create user from persona');

        if ($this->persona->user_id) {
            $this->alert('warning', 'Esta persona ya tiene un usuario asociado');
            return;
        }

        if (!$this->persona->email) {
            $this->alert('error', 'La persona debe tener un correo electrónico para crear un usuario');
            return;
        }

        $this->showCreateUserModal = true;
    }

    public function createUser(): void
    {
        $this->authorize('create user from persona');

        if ($this->persona->user_id) {
            $this->alert('warning', 'Esta persona ya tiene un usuario asociado');
            return;
        }

        if (!$this->persona->email) {
            $this->alert('error', 'La persona debe tener un correo electrónico para crear un usuario');
            return;
        }

        try {
            // Generar username único
            $baseUsername = Str::slug($this->persona->nombres . ' ' . $this->persona->apellidos);
            $username = $baseUsername;
            $counter = 1;

            while (User::where('username', $username)->exists()) {
                $username = $baseUsername . $counter;
                $counter++;
            }

            // Generar contraseña temporal
            $temporaryPassword = Str::random(12);

            // Crear usuario
            $user = User::create([
                'name' => $this->persona->nombre_completo,
                'username' => $username,
                'email' => $this->persona->email,
                'password' => Hash::make($temporaryPassword),
                'locale' => 'es',
                'email_verified_at' => now(),
            ]);

            // Asignar rol según el tipo de persona
            $roleName = match($this->persona->tipo_persona) {
                'militante' => 'Militante',
                'votante' => 'Votante',
                default => 'Votante'
            };

            if ($this->persona->es_lider_1x10) {
                $roleName = 'Líder 1x10';
            }

            // Asignar rol si existe
            if ($user->hasRole($roleName) === false) {
                $user->assignRole($roleName);
            }

            // Asociar usuario con persona
            $this->persona->update(['user_id' => $user->id]);

            // Aquí podrías enviar un email con las credenciales
            // Mail::to($user->email)->send(new UserCredentials($user, $temporaryPassword));

            $this->showCreateUserModal = false;
            $this->persona->refresh();

            $this->alert('success', __('personas.usuario_creado') . ". Usuario: {$username}, Contraseña temporal: {$temporaryPassword}");

        } catch (\Exception $e) {
            $this->alert('error', 'Error al crear el usuario: ' . $e->getMessage());
        }
    }

    public function deletePersona(): void
    {
        $this->authorize('delete personas');

        // Verificar si tiene personas asignadas
        if ($this->persona->personasAsignadas()->count() > 0) {
            $this->alert('error', 'No se puede eliminar una persona que tiene otras personas asignadas');
            return;
        }

        try {
            $this->persona->delete();
            $this->flash('success', __('personas.persona_eliminada'));
            $this->redirect(route('admin.personas.index'), navigate: true);
        } catch (\Exception $e) {
            $this->alert('error', __('personas.error_eliminar_persona'));
        }
    }

    #[Layout('components.layouts.admin')]
    public function render(): View
    {
        return view('livewire.admin.personas.pages.view-persona');
    }
}
