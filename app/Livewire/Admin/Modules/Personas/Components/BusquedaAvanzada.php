<?php

namespace App\Livewire\Admin\Personas\Components;

use App\Models\CentroVotacion;
use App\Models\Estado;
use App\Models\Municipio;
use App\Models\Parroquia;
use App\Models\Persona;
use Illuminate\Contracts\View\View;
use Jantinnerezo\LivewireAlert\LivewireAlert;
use Livewire\Attributes\Layout;
use Livewire\Attributes\Session;
use Livewire\Component;
use Livewire\WithPagination;

class BusquedaAvanzada extends Component
{
    use LivewireAlert;
    use WithPagination;

    #[Session]
    public int $perPage = 25;

    // Filtros de búsqueda
    public string $nombres = '';
    public string $apellidos = '';
    public string $cedula = '';
    public string $email = '';
    public string $telefono = '';

    // Filtros de ubicación
    public ?int $estado_id = null;
    public ?int $municipio_id = null;
    public ?int $parroquia_id = null;
    public ?int $centro_votacion_id = null;

    // Filtros de clasificación
    public string $tipo_persona = '';
    public string $estado_persona = '';
    public string $es_lider_1x10 = '';
    public string $tiene_lider = '';
    public string $tiene_usuario = '';

    // Filtros de fechas
    public ?string $fecha_nacimiento_desde = null;
    public ?string $fecha_nacimiento_hasta = null;
    public ?string $fecha_registro_desde = null;
    public ?string $fecha_registro_hasta = null;

    // Filtros de edad
    public ?int $edad_minima = null;
    public ?int $edad_maxima = null;

    public bool $mostrarResultados = false;

    public function mount(): void
    {
        $this->authorize('view personas');
    }

    public function updatedEstadoId(): void
    {
        $this->municipio_id = null;
        $this->parroquia_id = null;
        $this->centro_votacion_id = null;
        $this->resetPage();
    }

    public function updatedMunicipioId(): void
    {
        $this->parroquia_id = null;
        $this->centro_votacion_id = null;
        $this->resetPage();
    }

    public function updatedParroquiaId(): void
    {
        $this->centro_votacion_id = null;
        $this->resetPage();
    }

    public function buscar(): void
    {
        $this->mostrarResultados = true;
        $this->resetPage();
    }

    public function limpiarFiltros(): void
    {
        $this->reset([
            'nombres', 'apellidos', 'cedula', 'email', 'telefono',
            'estado_id', 'municipio_id', 'parroquia_id', 'centro_votacion_id',
            'tipo_persona', 'estado_persona', 'es_lider_1x10', 'tiene_lider', 'tiene_usuario',
            'fecha_nacimiento_desde', 'fecha_nacimiento_hasta',
            'fecha_registro_desde', 'fecha_registro_hasta',
            'edad_minima', 'edad_maxima'
        ]);
        $this->mostrarResultados = false;
        $this->resetPage();
    }

    public function exportarResultados(): void
    {
        $this->authorize('export personas');

        if (!$this->mostrarResultados) {
            $this->alert('warning', 'Debe realizar una búsqueda antes de exportar');
            return;
        }

        // Aquí implementarías la lógica de exportación
        $this->alert('info', 'Funcionalidad de exportación en desarrollo');
    }

    public function getMunicipiosProperty()
    {
        if (!$this->estado_id) {
            return collect();
        }

        return Municipio::where('estado_id', $this->estado_id)
            ->activos()
            ->orderBy('nombre')
            ->get();
    }

    public function getParroquiasProperty()
    {
        if (!$this->municipio_id) {
            return collect();
        }

        return Parroquia::where('municipio_id', $this->municipio_id)
            ->activos()
            ->orderBy('nombre')
            ->get();
    }

    public function getCentrosVotacionProperty()
    {
        if (!$this->parroquia_id) {
            return collect();
        }

        return CentroVotacion::where('parroquia_id', $this->parroquia_id)
            ->activos()
            ->orderBy('nombre')
            ->get();
    }

    public function getPersonasProperty()
    {
        if (!$this->mostrarResultados) {
            return collect();
        }

        $query = Persona::query()
            ->with(['estado', 'municipio', 'parroquia', 'centroVotacion', 'liderAsignado', 'user']);

        // Filtros de texto
        if ($this->nombres) {
            $query->where('nombres', 'like', "%{$this->nombres}%");
        }

        if ($this->apellidos) {
            $query->where('apellidos', 'like', "%{$this->apellidos}%");
        }

        if ($this->cedula) {
            $query->where('cedula', 'like', "%{$this->cedula}%");
        }

        if ($this->email) {
            $query->where('email', 'like', "%{$this->email}%");
        }

        if ($this->telefono) {
            $query->where(function ($q) {
                $q->where('telefono', 'like', "%{$this->telefono}%")
                  ->orWhere('telefono_secundario', 'like', "%{$this->telefono}%");
            });
        }

        // Filtros de ubicación
        if ($this->estado_id) {
            $query->where('estado_id', $this->estado_id);
        }

        if ($this->municipio_id) {
            $query->where('municipio_id', $this->municipio_id);
        }

        if ($this->parroquia_id) {
            $query->where('parroquia_id', $this->parroquia_id);
        }

        if ($this->centro_votacion_id) {
            $query->where('centro_votacion_id', $this->centro_votacion_id);
        }

        // Filtros de clasificación
        if ($this->tipo_persona) {
            $query->where('tipo_persona', $this->tipo_persona);
        }

        if ($this->estado_persona) {
            $query->where('estado', $this->estado_persona);
        }

        if ($this->es_lider_1x10 !== '') {
            $query->where('es_lider_1x10', $this->es_lider_1x10 === '1');
        }

        if ($this->tiene_lider !== '') {
            if ($this->tiene_lider === '1') {
                $query->whereNotNull('lider_asignado_id');
            } else {
                $query->whereNull('lider_asignado_id');
            }
        }

        if ($this->tiene_usuario !== '') {
            if ($this->tiene_usuario === '1') {
                $query->whereNotNull('user_id');
            } else {
                $query->whereNull('user_id');
            }
        }

        // Filtros de fechas
        if ($this->fecha_nacimiento_desde) {
            $query->where('fecha_nacimiento', '>=', $this->fecha_nacimiento_desde);
        }

        if ($this->fecha_nacimiento_hasta) {
            $query->where('fecha_nacimiento', '<=', $this->fecha_nacimiento_hasta);
        }

        if ($this->fecha_registro_desde) {
            $query->where('created_at', '>=', $this->fecha_registro_desde . ' 00:00:00');
        }

        if ($this->fecha_registro_hasta) {
            $query->where('created_at', '<=', $this->fecha_registro_hasta . ' 23:59:59');
        }

        // Filtros de edad (requiere cálculo)
        if ($this->edad_minima || $this->edad_maxima) {
            $query->whereNotNull('fecha_nacimiento');

            if ($this->edad_minima) {
                $fechaMaxima = now()->subYears($this->edad_minima)->format('Y-m-d');
                $query->where('fecha_nacimiento', '<=', $fechaMaxima);
            }

            if ($this->edad_maxima) {
                $fechaMinima = now()->subYears($this->edad_maxima + 1)->addDay()->format('Y-m-d');
                $query->where('fecha_nacimiento', '>=', $fechaMinima);
            }
        }

        return $query->orderBy('nombres')
            ->orderBy('apellidos')
            ->paginate($this->perPage);
    }

    #[Layout('components.layouts.admin')]
    public function render(): View
    {
        return view('livewire.admin.personas.components.busqueda-avanzada', [
            'estados' => Estado::activos()->orderBy('nombre')->get(),
            'municipios' => $this->municipios,
            'parroquias' => $this->parroquias,
            'centrosVotacion' => $this->centrosVotacion,
            'personas' => $this->personas,
        ]);
    }
}
