<?php

namespace App\Livewire\Admin\Unoxdiez\Persons\Pages;

use Illuminate\Contracts\View\View;
use Livewire\Attributes\Layout;
use Livewire\Component;

class IndexPersons extends Component
{

    public function mount(): void
    {
        //
    }

    #[Layout('components.layouts.app')]
    public function render(): View
    {
        return view('livewire.admin.unoxdiez.persons.pages.index-persons');
    }
}
