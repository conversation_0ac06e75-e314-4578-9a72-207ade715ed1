<?php

namespace App\Livewire\Admin;

use App\Models\Persona;
use App\Models\Estado;
use App\Models\Municipio;
use App\Models\Parroquia;
use Illuminate\Contracts\View\View;
use Jantinnerezo\LivewireAlert\LivewireAlert;
use Livewire\Attributes\Layout;
use Livewire\Attributes\Session;
use Livewire\Attributes\Url;
use Livewire\Component;
use Livewire\WithPagination;

class Personas extends Component
{
    use LivewireAlert;
    use WithPagination;

    /** @var array<string,string> */
    protected $listeners = [
        'personaDeleted' => '$refresh',
        'personaCreated' => '$refresh',
        'personaUpdated' => '$refresh',
    ];

    #[Session]
    public int $perPage = 15;

    /** @var array<int,string> */
    public array $searchableFields = ['nombres', 'apellidos', 'cedula', 'email', 'telefono'];

    #[Url]
    public string $search = '';

    #[Url]
    public string $tipoPersona = '';

    #[Url]
    public string $estadoPersona = '';

    #[Url]
    public ?int $estadoId = null;

    #[Url]
    public ?int $municipioId = null;

    #[Url]
    public ?int $parroquiaId = null;

    #[Url]
    public string $esLider = '';

    #[Url]
    public string $activeTab = 'todos';

    public function mount(): void
    {
        $this->authorize('view personas');
    }

    public function updatingSearch(): void
    {
        $this->resetPage();
    }

    public function updatingTipoPersona(): void
    {
        $this->resetPage();
    }

    public function updatingEstadoPersona(): void
    {
        $this->resetPage();
    }

    public function updatingEstadoId(): void
    {
        $this->municipioId = null;
        $this->parroquiaId = null;
        $this->resetPage();
    }

    public function updatingMunicipioId(): void
    {
        $this->parroquiaId = null;
        $this->resetPage();
    }

    public function updatingParroquiaId(): void
    {
        $this->resetPage();
    }

    public function updatingActiveTab(): void
    {
        $this->resetPage();

        // Limpiar filtros específicos cuando se cambia de pestaña
        $this->tipoPersona = '';
        $this->esLider = '';
    }

    public function setActiveTab(string $tab): void
    {
        $this->activeTab = $tab;
        $this->resetPage();

        // Ajustar filtros según la pestaña
        switch ($tab) {
            case 'militantes':
                $this->tipoPersona = 'militante';
                break;
            case 'votantes':
                $this->tipoPersona = 'votante';
                break;
            case 'simpatizantes':
                $this->tipoPersona = 'simpatizante';
                break;
            case 'lideres':
                $this->esLider = '1';
                $this->tipoPersona = '';
                break;
            default:
                $this->tipoPersona = '';
                $this->esLider = '';
        }
    }

    public function clearFilters(): void
    {
        $this->search = '';
        $this->tipoPersona = '';
        $this->estadoPersona = '';
        $this->estadoId = null;
        $this->municipioId = null;
        $this->parroquiaId = null;
        $this->esLider = '';
        $this->activeTab = 'todos';
        $this->resetPage();
    }

    public function deletePersona(int $personaId): void
    {
        $this->authorize('delete personas');

        $persona = Persona::findOrFail($personaId);

        // Verificar si tiene personas asignadas
        if ($persona->personasAsignadas()->count() > 0) {
            $this->alert('error', 'No se puede eliminar una persona que tiene otras personas asignadas');
            return;
        }

        $persona->delete();

        $this->alert('success', __('personas.persona_eliminada'));
        $this->dispatch('personaDeleted');
    }

    public function exportPersonas(): void
    {
        $this->authorize('export personas');

        // Aquí implementarías la lógica de exportación
        $this->alert('info', __('personas.exportacion_en_desarrollo'));
    }

    public function getMunicipiosProperty()
    {
        if (!$this->estadoId) {
            return collect();
        }

        return Municipio::where('estado_id', $this->estadoId)
            ->activos()
            ->orderBy('nombre')
            ->get();
    }

    public function getParroquiasProperty()
    {
        if (!$this->municipioId) {
            return collect();
        }

        return Parroquia::where('municipio_id', $this->municipioId)
            ->activos()
            ->orderBy('nombre')
            ->get();
    }

    #[Layout('components.layouts.admin')]
    public function render(): View
    {
        $query = Persona::query()
            ->with(['estado', 'municipio', 'parroquia', 'centroVotacion', 'liderAsignado', 'user'])
            ->when($this->search, function ($query, $search) {
                $query->buscarTexto($search);
            })
            ->when($this->estadoPersona, function ($query, $estado) {
                $query->where('estado', $estado);
            })
            ->when($this->estadoId, function ($query, $estadoId) {
                $query->where('estado_id', $estadoId);
            })
            ->when($this->municipioId, function ($query, $municipioId) {
                $query->where('municipio_id', $municipioId);
            })
            ->when($this->parroquiaId, function ($query, $parroquiaId) {
                $query->where('parroquia_id', $parroquiaId);
            });

        // Aplicar filtros según la pestaña activa
        switch ($this->activeTab) {
            case 'militantes':
                $query->where('tipo_persona', 'militante');
                break;
            case 'votantes':
                $query->where('tipo_persona', 'votante');
                break;
            case 'simpatizantes':
                $query->where('tipo_persona', 'simpatizante');
                break;
            case 'lideres':
                $query->where('es_lider_1x10', true);
                break;
            default:
                // Para la pestaña "todos", aplicar filtros adicionales si están definidos
                if ($this->tipoPersona) {
                    $query->where('tipo_persona', $this->tipoPersona);
                }
                if ($this->esLider === '1') {
                    $query->where('es_lider_1x10', true);
                } elseif ($this->esLider === '0') {
                    $query->where('es_lider_1x10', false);
                }
        }

        $personas = $query->orderBy('nombres')
            ->orderBy('apellidos')
            ->paginate($this->perPage);

        // Estadísticas para las pestañas
        $stats = [
            'total' => Persona::count(),
            'militantes' => Persona::militantes()->count(),
            'votantes' => Persona::votantes()->count(),
            'simpatizantes' => Persona::where('tipo_persona', 'simpatizante')->count(),
            'lideres' => Persona::lideres1x10()->count(),
        ];

        return view('livewire.admin.personas', [
            'personas' => $personas,
            'estados' => Estado::activos()->orderBy('nombre')->get(),
            'municipios' => $this->municipios,
            'parroquias' => $this->parroquias,
            'stats' => $stats,
        ]);
    }
}
