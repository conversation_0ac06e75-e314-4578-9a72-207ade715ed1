<?php

namespace App\Helpers;

use Illuminate\Support\Facades\Gate;

class MenuHelper
{
    /**
     * Obtiene los elementos del menú filtrados por permisos
     *
     * @return array
     */
    public static function getMenuItems(): array
    {
        $menuConfig = config('menu.items', []);
        $filteredMenu = [];

        foreach ($menuConfig as $group) {
            if ($group['type'] === 'group') {
                $filteredGroup = self::filterGroupByPermissions($group);
                
                if ($filteredGroup && !empty($filteredGroup['items'])) {
                    $filteredMenu[] = $filteredGroup;
                }
            }
        }

        return $filteredMenu;
    }

    /**
     * Filtra un grupo del menú por permisos
     *
     * @param array $group
     * @return array|null
     */
    private static function filterGroupByPermissions(array $group): ?array
    {
        // Verificar permisos del grupo
        if (isset($group['permission'])) {
            $hasGroupPermission = self::checkPermission($group['permission'], $group['permission_type'] ?? 'single');
            
            if (!$hasGroupPermission) {
                return null;
            }
        }

        // Filtrar items del grupo
        $filteredItems = [];
        foreach ($group['items'] as $item) {
            if ($item['type'] === 'item') {
                if (isset($item['permission'])) {
                    $hasItemPermission = self::checkPermission($item['permission']);
                    
                    if ($hasItemPermission) {
                        $filteredItems[] = $item;
                    }
                } else {
                    $filteredItems[] = $item;
                }
            }
        }

        if (empty($filteredItems)) {
            return null;
        }

        $group['items'] = $filteredItems;
        return $group;
    }

    /**
     * Verifica permisos
     *
     * @param string|array $permission
     * @param string $type
     * @return bool
     */
    private static function checkPermission($permission, string $type = 'single'): bool
    {
        if (is_array($permission)) {
            return $type === 'any' ? Gate::any($permission) : Gate::check($permission);
        }

        return Gate::allows($permission);
    }

    /**
     * Obtiene la configuración de favoritos
     *
     * @return array
     */
    public static function getFavoritesConfig(): array
    {
        return config('menu.favorites', []);
    }

    /**
     * Obtiene la configuración de enlaces externos
     *
     * @return array
     */
    public static function getExternalLinksConfig(): array
    {
        return config('menu.external_links', []);
    }

    /**
     * Agrega un nuevo elemento al menú dinámicamente
     *
     * @param string $groupHeading
     * @param array $item
     * @return void
     */
    public static function addMenuItem(string $groupHeading, array $item): void
    {
        $menuConfig = config('menu.items', []);
        
        // Buscar el grupo existente
        foreach ($menuConfig as &$group) {
            if (($group['heading'] ?? '') === $groupHeading || 
                (isset($group['heading_key']) && __($group['heading_key']) === $groupHeading)) {
                $group['items'][] = $item;
                break;
            }
        }
        
        // Si no se encuentra el grupo, crear uno nuevo
        if (!isset($group)) {
            $menuConfig[] = [
                'type' => 'group',
                'heading' => $groupHeading,
                'class' => 'grid',
                'items' => [$item]
            ];
        }
        
        config(['menu.items' => $menuConfig]);
    }

    /**
     * Verifica si una ruta está activa
     *
     * @param string $routePattern
     * @return bool
     */
    public static function isRouteActive(string $routePattern): bool
    {
        return request()->routeIs($routePattern);
    }

    /**
     * Obtiene el ícono para un tipo de elemento
     *
     * @param string $type
     * @return string
     */
    public static function getIconForType(string $type): string
    {
        $icons = [
            'dashboard' => 'home',
            'users' => 'user',
            'roles' => 'shield-user',
            'permissions' => 'shield-check',
            'personas' => 'users',
            'search' => 'magnifying-glass',
            'create' => 'plus',
            'edit' => 'pencil',
            'view' => 'eye',
            'delete' => 'trash',
            'settings' => 'cog',
            'logout' => 'arrow-right-start-on-rectangle',
        ];

        return $icons[$type] ?? 'circle';
    }
}
